
import Farm from './Farm.js';
import User from './User.js';
import UserFarm from './UserFarm.js';
import InventoryItem from './InventoryItem.js';
import InventoryCategory from './InventoryCategory.js';
import InventoryTransaction from './InventoryTransaction.js';
import Product from './Product.js';
import SeedProduct from './SeedProduct.js';
import ChemicalProduct from './ChemicalProduct.js';
import Equipment from './Equipment.js';
import EquipmentSharing from './EquipmentSharing.js';
import EquipmentTelematics from './EquipmentTelematics.js';
import Document from './Document.js';
import DocumentFolder from './DocumentFolder.js';
import DocumentPermission from './DocumentPermission.js';
import Livestock from './Livestock.js';
import LivestockGroup from './LivestockGroup.js';
import Crop from './Crop.js';
import CropActivity from './CropActivity.js';
import IoTDevice from './IoTDevice.js';
import IoTData from './IoTData.js';
import Integration from './Integration.js';
import Role from './Role.js';
import RolePermission from './RolePermission.js';
import Supplier from './Supplier.js';
import Order from './Order.js';
import OrderItem from './OrderItem.js';
import ServiceProvider from './ServiceProvider.js';
import ServiceRequest from './ServiceRequest.js';
import Vet from './Vet.js';
import Vendor from './Vendor.js';
import Field from './Field.js';
import SupportTicket from './SupportTicket.js';
import SupportTicketComment from './SupportTicketComment.js';
import SupportTicketAttachment from './SupportTicketAttachment.js';
import HelpGuide from './HelpGuide.js';
import HelpTip from './HelpTip.js';
import UserHelpTipDismissal from './UserHelpTipDismissal.js';
import GettingStartedTask from './GettingStartedTask.js';
import UserGettingStartedProgress from './UserGettingStartedProgress.js';
import MigrationSystem from './MigrationSystem.js';
import MigrationJob from './MigrationJob.js';
import MigrationResult from './MigrationResult.js';
import DatabaseMigration from './DatabaseMigration.js';
import Receipt from './Receipt.js';
import Driver from './Driver.js';
import Delivery from './Delivery.js';
import Pickup from './Pickup.js';
import DriverSchedule from './DriverSchedule.js';
import DriverLocation from './DriverLocation.js';
import {Customer, Expense} from "./index.js";
import CropType from './CropType.js';
import Harvest from './Harvest.js';
import MarketContract from './MarketContract.js';
import PriceComparison from './PriceComparison.js';
import MarketTrend from './MarketTrend.js';
import MarketplaceListing from './MarketplaceListing.js';
import SupplierProduct from "./SupplierProduct.js";
import SupplierReview from "./SupplierReview.js";
import DashboardLayout from './DashboardLayout.js';
import FarmDashboardLayout from './FarmDashboardLayout.js';
import GlobalDashboardLayout from './GlobalDashboardLayout.js';
import Alert from './Alert.js';
import AlertRule from './AlertRule.js';
import SubscriptionPlan from './SubscriptionPlan.js';
import Employee from './Employee.js';
import MarketPrice from './MarketPrice.js';
import FuturePrice from './FuturePrice.js';
import HistoricalPrice from './HistoricalPrice.js';
import HarvestDirectionMap from './HarvestDirectionMap.js';
import TaxCategory from './TaxCategory.js';
import TaxDeduction from './TaxDeduction.js';
import TaxDocument from './TaxDocument.js';
import EmployeeTaxInfo from './EmployeeTaxInfo.js';
import ContractorTaxInfo from './ContractorTaxInfo.js';
import TaxPayment from './TaxPayment.js';
import TaxFiling from './TaxFiling.js';
import SignableDocument from './SignableDocument.js';
import DocumentSigner from './DocumentSigner.js';
import DocumentSignature from './DocumentSignature.js';
import DocumentField from './DocumentField.js';
import DocumentAuditLog from './DocumentAuditLog.js';
import DocumentBlockchainVerification from './DocumentBlockchainVerification.js';
import Invoice from './Invoice.js';
import InvoiceItem from './InvoiceItem.js';

// Set up associations that require all models to be loaded first
export const setupAssociations = () => {
  // User-Farm associations
  User.belongsToMany(Farm, { through: UserFarm, foreignKey: 'user_id' });
  Farm.belongsToMany(User, { through: UserFarm, foreignKey: 'farm_id' });

  // User-SubscriptionPlan associations
  User.belongsTo(SubscriptionPlan, { foreignKey: 'subscription_plan_id' });
  SubscriptionPlan.hasMany(User, { foreignKey: 'subscription_plan_id' });

  // Farm-SubscriptionPlan associations
  Farm.belongsTo(SubscriptionPlan, { foreignKey: 'subscription_plan_id' });
  SubscriptionPlan.hasMany(Farm, { foreignKey: 'subscription_plan_id' });

  // Direct association between Farm and UserFarm
  Farm.hasMany(UserFarm, { foreignKey: 'farm_id', as: 'UserFarm' });
  UserFarm.belongsTo(Farm, { foreignKey: 'farm_id' });

  // UserFarm-Role associations
  UserFarm.belongsTo(Role, { foreignKey: 'role_id' });
  Role.hasMany(UserFarm, { foreignKey: 'role_id', as: 'userFarms' });

  // Employee associations
  Employee.belongsTo(Farm, { foreignKey: 'farm_id' });
  Employee.belongsTo(User, { foreignKey: 'user_id', onDelete: 'CASCADE' });
  Farm.hasMany(Employee, { foreignKey: 'farm_id', as: 'employees' });
  User.hasMany(Employee, { foreignKey: 'user_id', as: 'employeeRoles', onDelete: 'CASCADE' });

  // InventoryItem associations
  InventoryItem.belongsTo(InventoryCategory, { foreignKey: 'category_id', as: 'category' });
  InventoryCategory.hasMany(InventoryItem, { foreignKey: 'category_id', as: 'items' });

  InventoryItem.belongsTo(Farm, { foreignKey: 'farm_id', as: 'inventoryItemFarm' });
  Farm.hasMany(InventoryItem, { foreignKey: 'farm_id', as: 'inventoryItems' });

  // InventoryCategory associations
  InventoryCategory.belongsTo(Farm, { foreignKey: 'farm_id', as: 'inventoryCategoryFarm' });
  Farm.hasMany(InventoryCategory, { foreignKey: 'farm_id', as: 'categories' });

  InventoryItem.hasMany(InventoryTransaction, { foreignKey: 'inventory_item_id', as: 'transactions' });
  InventoryTransaction.belongsTo(InventoryItem, { foreignKey: 'inventory_item_id', as: 'item' });
  InventoryTransaction.belongsTo(User, { foreignKey: 'user_id' });
  User.hasMany(InventoryTransaction, { foreignKey: 'user_id', as: 'inventoryTransactions' });

  // Product associations
  Product.belongsTo(Farm, { foreignKey: 'farm_id', as: 'productFarm' });
  Farm.hasMany(Product, { foreignKey: 'farm_id', as: 'products' });

  // SeedProduct and ChemicalProduct associations
  SeedProduct.belongsTo(Product, { foreignKey: 'product_id', as: 'seedProduct' });
  Product.hasOne(SeedProduct, { foreignKey: 'product_id' });
  SeedProduct.belongsTo(Farm, { foreignKey: 'farm_id' });
  Farm.hasMany(SeedProduct, { foreignKey: 'farm_id' });

  ChemicalProduct.belongsTo(Product, { foreignKey: 'product_id', as: 'chemicalProduct' });
  Product.hasOne(ChemicalProduct, { foreignKey: 'product_id' });
  ChemicalProduct.belongsTo(Farm, { foreignKey: 'farm_id' });
  Farm.hasMany(ChemicalProduct, { foreignKey: 'farm_id' });

  // Equipment associations
  Equipment.belongsTo(Farm, { foreignKey: 'farm_id', as: 'equipmentFarm' });
  Farm.hasMany(Equipment, { foreignKey: 'farm_id', as: 'equipment' });

  Equipment.hasMany(EquipmentTelematics, { foreignKey: 'equipment_id', as: 'telematics' });
  EquipmentTelematics.belongsTo(Equipment, { foreignKey: 'equipment_id', as: 'telematicsEquipment' });

  // Equipment Sharing associations
  EquipmentSharing.belongsTo(Equipment, { foreignKey: 'equipment_id', as: 'sharingEquipment' });
  Equipment.hasMany(EquipmentSharing, { foreignKey: 'equipment_id', as: 'sharing' });

  EquipmentSharing.belongsTo(Farm, { as: 'Owner', foreignKey: 'owner_farm_id' });
  EquipmentSharing.belongsTo(Farm, { as: 'Renter', foreignKey: 'renter_farm_id' });

  Farm.hasMany(EquipmentSharing, { as: 'EquipmentLentOut', foreignKey: 'owner_farm_id' });
  Farm.hasMany(EquipmentSharing, { as: 'EquipmentBorrowed', foreignKey: 'renter_farm_id' });

  // Document management system associations
  Document.belongsTo(User, { foreignKey: 'uploaded_by', as: 'uploader', onDelete: 'CASCADE' });
  Document.belongsTo(Farm, { foreignKey: 'farm_id', as: 'documentFarm' });
  Document.belongsTo(DocumentFolder, { foreignKey: 'folder_id', as: 'documentFolder' });

  DocumentFolder.hasMany(Document, { foreignKey: 'folder_id', as: 'folderDocuments' });
  DocumentFolder.belongsTo(Farm, { foreignKey: 'farm_id', as: 'documentFolderFarm' });

  DocumentPermission.belongsTo(Document, { foreignKey: 'document_id', as: 'permissionDocument' });
  DocumentPermission.belongsTo(User, { foreignKey: 'user_id', as: 'permissionUser', onDelete: 'CASCADE' });

  Document.hasMany(DocumentPermission, { foreignKey: 'document_id', as: 'permissions' });
  User.hasMany(DocumentPermission, { foreignKey: 'user_id', as: 'documentPermissions', onDelete: 'CASCADE' });

  // Livestock associations
  Livestock.belongsTo(Farm, { foreignKey: 'farm_id', as: 'livestockFarm' });
  Farm.hasMany(Livestock, { foreignKey: 'farm_id', as: 'livestock' });

  // LivestockGroup associations
  LivestockGroup.belongsTo(Farm, { foreignKey: 'farm_id', as: 'livestockGroupFarm' });
  Farm.hasMany(LivestockGroup, { foreignKey: 'farm_id', as: 'livestockGroups' });

  // Crop associations
  Crop.belongsTo(Farm, { foreignKey: 'farm_id', as: 'cropFarm' });
  Farm.hasMany(Crop, { foreignKey: 'farm_id', as: 'crops' });

  Crop.hasMany(CropActivity, { foreignKey: 'crop_id', as: 'activities' });
  CropActivity.belongsTo(Crop, { foreignKey: 'crop_id', as: 'crop' });

  // CropType associations
  CropType.belongsTo(Farm, { foreignKey: 'farm_id', as: 'cropTypeFarm' });
  Farm.hasMany(CropType, { foreignKey: 'farm_id', as: 'cropTypes' });

  // Harvest associations
  Harvest.belongsTo(Farm, { foreignKey: 'farm_id', as: 'harvestFarm' });
  Farm.hasMany(Harvest, { foreignKey: 'farm_id', as: 'harvests' });

  Harvest.belongsTo(Field, { foreignKey: 'field_id', as: 'harvestField' });
  Field.hasMany(Harvest, { foreignKey: 'field_id', as: 'harvests' });

  Harvest.belongsTo(Crop, { foreignKey: 'crop_id', as: 'harvestCrop' });
  Crop.hasMany(Harvest, { foreignKey: 'crop_id', as: 'harvests' });

  // IoT associations
  IoTDevice.belongsTo(Farm, { foreignKey: 'farm_id', as: 'iotDeviceFarm' });
  Farm.hasMany(IoTDevice, { foreignKey: 'farm_id', as: 'iotDevices' });

  IoTDevice.hasMany(IoTData, { foreignKey: 'device_id', as: 'data' });
  IoTData.belongsTo(IoTDevice, { foreignKey: 'device_id', as: 'device' });

  // Integration associations
  Integration.belongsTo(Farm, { foreignKey: 'farm_id', as: 'integrationFarm' });
  Farm.hasMany(Integration, { foreignKey: 'farm_id', as: 'integrations' });

  // Role Permission associations
  RolePermission.belongsTo(Farm, { foreignKey: 'farm_id', as: 'rolePermissionFarm' });
  Farm.hasMany(RolePermission, { foreignKey: 'farm_id', as: 'farmRolePermissions' });

  // Alert associations
  Alert.belongsTo(Farm, { foreignKey: 'farm_id' });
  Alert.belongsTo(User, { foreignKey: 'user_id' });
  Farm.hasMany(Alert, { foreignKey: 'farm_id', as: 'alerts' });
  User.hasMany(Alert, { foreignKey: 'user_id', as: 'alerts' });

  // Alert Rule associations
  AlertRule.belongsTo(Farm, { foreignKey: 'farm_id' });
  Farm.hasMany(AlertRule, { foreignKey: 'farm_id', as: 'alertRules' });

  // Supplier associations
  Supplier.belongsTo(Farm, { foreignKey: 'farm_id', as: 'supplierFarm' });
  Farm.hasMany(Supplier, { foreignKey: 'farm_id', as: 'suppliers' });
  Supplier.belongsTo(User, { foreignKey: 'user_id', onDelete: 'CASCADE' });
  User.hasMany(Supplier, { foreignKey: 'user_id', onDelete: 'CASCADE' });

  // SupplierProduct associations
  SupplierProduct.belongsTo(Supplier, { foreignKey: 'supplier_id' });
  Supplier.hasMany(SupplierProduct, { foreignKey: 'supplier_id' });
  SupplierProduct.belongsTo(Product, { foreignKey: 'product_id' });
  Product.hasMany(SupplierProduct, { foreignKey: 'product_id' });

  // SupplierReview associations
  SupplierReview.belongsTo(Supplier, { foreignKey: 'supplier_id' });
  Supplier.hasMany(SupplierReview, { foreignKey: 'supplier_id' });
  SupplierReview.belongsTo(User, { foreignKey: 'user_id', onDelete: 'CASCADE' });
  User.hasMany(SupplierReview, { foreignKey: 'user_id', onDelete: 'CASCADE' });

  // Order associations
  Order.belongsTo(Farm, { foreignKey: 'farm_id', as: 'orderFarm' });
  Order.belongsTo(Supplier, { foreignKey: 'supplier_id', as: 'orderSupplier' });
  Order.belongsTo(User, { foreignKey: 'user_id', as: 'creator', onDelete: 'CASCADE' });
  Farm.hasMany(Order, { foreignKey: 'farm_id', as: 'orders' });
  Supplier.hasMany(Order, { foreignKey: 'supplier_id', as: 'supplierOrders' });
  User.hasMany(Order, { foreignKey: 'user_id', as: 'createdOrders', onDelete: 'CASCADE' });

  Order.hasMany(OrderItem, { foreignKey: 'order_id', as: 'items' });
  OrderItem.belongsTo(Order, { foreignKey: 'order_id', as: 'orderItemOrder' });

  // OrderItem-InventoryItem associations
  OrderItem.belongsTo(InventoryItem, { foreignKey: 'inventory_item_id' });
  InventoryItem.hasMany(OrderItem, { foreignKey: 'inventory_item_id' });

  // Service Provider associations
  ServiceProvider.belongsTo(Farm, { foreignKey: 'farm_id', as: 'serviceProviderFarm' });
  Farm.hasMany(ServiceProvider, { foreignKey: 'farm_id', as: 'serviceProviders' });

  // Service Request associations
  ServiceRequest.belongsTo(Farm, { foreignKey: 'farm_id', as: 'serviceRequestFarm' });
  ServiceRequest.belongsTo(ServiceProvider, { foreignKey: 'provider_id', as: 'provider' });
  Farm.hasMany(ServiceRequest, { foreignKey: 'farm_id', as: 'serviceRequests' });
  ServiceProvider.hasMany(ServiceRequest, { foreignKey: 'provider_id', as: 'providerServiceRequests' });

  // Vet associations
  Vet.belongsTo(Farm, { foreignKey: 'farm_id', as: 'vetFarm' });
  Farm.hasMany(Vet, { foreignKey: 'farm_id', as: 'vets' });

  // Vendor associations
  Vendor.belongsTo(Farm, { foreignKey: 'farm_id', as: 'vendorFarm' });
  Farm.hasMany(Vendor, { foreignKey: 'farm_id', as: 'vendors' });
  Vendor.belongsTo(User, { foreignKey: 'user_id', onDelete: 'CASCADE' });
  User.hasMany(Vendor, { foreignKey: 'user_id', onDelete: 'CASCADE' });

  // Field associations
  Field.belongsTo(Farm, { foreignKey: 'farm_id', as: 'fieldFarm' });
  Farm.hasMany(Field, { foreignKey: 'farm_id', as: 'fields' });

  // HarvestDirectionMap associations
  HarvestDirectionMap.belongsTo(Field, { foreignKey: 'field_id', as: 'field' });
  Field.hasMany(HarvestDirectionMap, { foreignKey: 'field_id', as: 'harvestDirectionMaps' });

  // Support Ticket associations
  SupportTicket.belongsTo(User, { foreignKey: 'user_id', as: 'creator', onDelete: 'CASCADE' });

  SupportTicket.belongsTo(User, { foreignKey: 'assigned_to', as: 'assignee', onDelete: 'CASCADE' });
  User.hasMany(SupportTicket, { foreignKey: 'assigned_to', as: 'assignedTickets', onDelete: 'CASCADE' });

  SupportTicket.belongsTo(Farm, { foreignKey: 'farm_id', as: 'supportTicketFarm' });
  Farm.hasMany(SupportTicket, { foreignKey: 'farm_id' });

  // Support Ticket Comment associations
  SupportTicketComment.belongsTo(SupportTicket, { foreignKey: 'ticket_id' });
  SupportTicket.hasMany(SupportTicketComment, { foreignKey: 'ticket_id', as: 'comments' });

  SupportTicketComment.belongsTo(User, { foreignKey: 'user_id', as: 'author', onDelete: 'CASCADE' });
  User.hasMany(SupportTicketComment, { foreignKey: 'user_id', as: 'ticketComments', onDelete: 'CASCADE' });

  // SupportTicketAttachment associations
  SupportTicketAttachment.belongsTo(SupportTicket, { foreignKey: 'ticket_id' });
  SupportTicket.hasMany(SupportTicketAttachment, { foreignKey: 'ticket_id', as: 'attachments' });

  SupportTicketAttachment.belongsTo(User, { foreignKey: 'user_id', as: 'uploader', onDelete: 'CASCADE' });
  User.hasMany(SupportTicketAttachment, { foreignKey: 'user_id', as: 'uploadedAttachments', onDelete: 'CASCADE' });

  // Help Tip Dismissal associations
  UserHelpTipDismissal.belongsTo(User, { foreignKey: 'user_id', onDelete: 'CASCADE' });
  User.hasMany(UserHelpTipDismissal, { foreignKey: 'user_id', as: 'dismissedHelpTips', onDelete: 'CASCADE' });

  UserHelpTipDismissal.belongsTo(HelpTip, { foreignKey: 'help_tip_id' });

  HelpTip.belongsTo(UserHelpTipDismissal, { foreignKey: 'help_tip_id' });
  HelpTip.hasMany(UserHelpTipDismissal, { foreignKey: 'help_tip_id', as: 'userDismissals' });

  // Getting Started Progress associations
  UserGettingStartedProgress.belongsTo(User, { foreignKey: 'user_id', onDelete: 'CASCADE' });
  User.hasMany(UserGettingStartedProgress, { foreignKey: 'user_id', as: 'gettingStartedProgress', onDelete: 'CASCADE' });

  UserGettingStartedProgress.belongsTo(GettingStartedTask, { foreignKey: 'task_id' });
  GettingStartedTask.hasMany(UserGettingStartedProgress, { foreignKey: 'task_id', as: 'userProgress' });

  // Migration System associations
  MigrationSystem.hasMany(MigrationJob, { foreignKey: 'source_system', as: 'jobs' });

  // Migration Job associations
  MigrationJob.belongsTo(User, { foreignKey: 'user_id', as: 'migrationJobUser', onDelete: 'CASCADE' });
  MigrationJob.belongsTo(Farm, { foreignKey: 'farm_id', as: 'migrationJobFarm' });
  MigrationJob.belongsTo(MigrationSystem, { foreignKey: 'source_system', as: 'system' });

  // Migration Result associations
  MigrationResult.belongsTo(MigrationJob, { foreignKey: 'job_id', as: 'job' });
  MigrationJob.hasOne(MigrationResult, { foreignKey: 'job_id', as: 'result' });

  // Farm and User associations with Migration Jobs
  Farm.hasMany(MigrationJob, { foreignKey: 'farm_id', as: 'farmMigrationJobs' });
  User.hasMany(MigrationJob, { foreignKey: 'user_id', as: 'userMigrationJobs', onDelete: 'CASCADE' });

  // Database Migration associations
  DatabaseMigration.belongsTo(User, { foreignKey: 'applied_by', as: 'appliedBy', onDelete: 'CASCADE' });
  User.hasMany(DatabaseMigration, { foreignKey: 'applied_by', as: 'appliedMigrations', onDelete: 'CASCADE' });

  // Dashboard Layout associations
  DashboardLayout.belongsTo(User, { foreignKey: 'user_id', onDelete: 'CASCADE' });
  User.hasOne(DashboardLayout, { foreignKey: 'user_id', onDelete: 'CASCADE' });

  // Farm Dashboard Layout associations
  FarmDashboardLayout.belongsTo(Farm, { foreignKey: 'farm_id' });
  Farm.hasOne(FarmDashboardLayout, { foreignKey: 'farm_id' });

  // Receipt associations
  Receipt.belongsTo(User, { foreignKey: 'uploaded_by', as: 'uploader', onDelete: 'CASCADE' });
  User.hasMany(Receipt, { foreignKey: 'uploaded_by', as: 'uploadedReceipts', onDelete: 'CASCADE' });

  Receipt.belongsTo(Farm, { foreignKey: 'farm_id', as: 'receiptFarm' });
  Farm.hasMany(Receipt, { foreignKey: 'farm_id', as: 'receipts' });

  Receipt.belongsTo(Expense, { foreignKey: 'expense_id', as: 'expense' });
  Expense.hasOne(Receipt, { foreignKey: 'expense_id', as: 'receipt' });

  // Expense associations
  Expense.belongsTo(Employee, { foreignKey: 'employee_id' });
  Expense.belongsTo(User, { foreignKey: 'reviewed_by', as: 'reviewer', onDelete: 'CASCADE' });
  Employee.hasMany(Expense, { foreignKey: 'employee_id', as: 'expenses' });
  User.hasMany(Expense, { foreignKey: 'reviewed_by', as: 'reviewedExpenses', onDelete: 'CASCADE' });

  // Transport Management System associations

  // Driver associations
  Driver.belongsTo(Farm, { foreignKey: 'farm_id', as: 'driverFarm' });
  Driver.belongsTo(User, { foreignKey: 'user_id', as: 'driverUser', onDelete: 'CASCADE' });
  Farm.hasMany(Driver, { foreignKey: 'farm_id', as: 'drivers' });
  User.hasOne(Driver, { foreignKey: 'user_id', as: 'driverUser', onDelete: 'CASCADE' });

  // Delivery associations
  Delivery.belongsTo(Farm, { foreignKey: 'farm_id', as: 'deliveryFarm' });
  Delivery.belongsTo(Driver, { foreignKey: 'driver_id', as: 'driver' });
  Delivery.belongsTo(Customer, { foreignKey: 'customer_id', as: 'deliveryCustomer' });
  Delivery.belongsTo(Order, { foreignKey: 'order_id', as: 'order' });
  Farm.hasMany(Delivery, { foreignKey: 'farm_id', as: 'deliveries' });
  Driver.hasMany(Delivery, { foreignKey: 'driver_id', as: 'deliveries' });
  Customer.hasMany(Delivery, { foreignKey: 'customer_id', as: 'deliveries' });
  Order.hasOne(Delivery, { foreignKey: 'order_id', as: 'delivery' });

  // Pickup associations
  Pickup.belongsTo(Farm, { foreignKey: 'farm_id', as: 'pickupFarm' });
  Pickup.belongsTo(Driver, { foreignKey: 'driver_id', as: 'pickupDriver' });
  Pickup.belongsTo(Supplier, { foreignKey: 'supplier_id', as: 'supplier' });
  Pickup.belongsTo(Customer, { foreignKey: 'customer_id', as: 'pickupCustomer' });
  Pickup.belongsTo(Order, { foreignKey: 'order_id', as: 'order' });
  Farm.hasMany(Pickup, { foreignKey: 'farm_id', as: 'pickups' });
  Driver.hasMany(Pickup, { foreignKey: 'driver_id', as: 'pickups' });
  Supplier.hasMany(Pickup, { foreignKey: 'supplier_id', as: 'pickups' });
  Customer.hasMany(Pickup, { foreignKey: 'customer_id', as: 'pickups' });
  Order.hasOne(Pickup, { foreignKey: 'order_id', as: 'pickup' });

  // DriverSchedule associations
  DriverSchedule.belongsTo(Farm, { foreignKey: 'farm_id', as: 'driverScheduleFarm' });
  DriverSchedule.belongsTo(Driver, { foreignKey: 'driver_id', as: 'driver' });
  DriverSchedule.belongsTo(Delivery, { foreignKey: 'delivery_id', as: 'delivery' });
  DriverSchedule.belongsTo(Pickup, { foreignKey: 'pickup_id', as: 'pickup' });
  Farm.hasMany(DriverSchedule, { foreignKey: 'farm_id', as: 'driverSchedules' });
  Driver.hasMany(DriverSchedule, { foreignKey: 'driver_id', as: 'schedules' });
  Delivery.hasOne(DriverSchedule, { foreignKey: 'delivery_id', as: 'deliverySchedule' });
  Pickup.hasOne(DriverSchedule, { foreignKey: 'pickup_id', as: 'pickup' });

  // DriverLocation associations
  DriverLocation.belongsTo(Farm, { foreignKey: 'farm_id', as: 'driverLocationFarm' });
  DriverLocation.belongsTo(Driver, { foreignKey: 'driver_id', as: 'locationDriver' });
  Farm.hasMany(DriverLocation, { foreignKey: 'farm_id', as: 'driverLocations' });
  Driver.hasMany(DriverLocation, { foreignKey: 'driver_id', as: 'locations' });

  // Market Integration associations

  // MarketContract associations
  MarketContract.belongsTo(Farm, { foreignKey: 'farm_id', as: 'marketContractFarm' });
  MarketContract.belongsTo(Supplier, { foreignKey: 'supplier_id', as: 'marketContractSupplier' });
  MarketContract.belongsTo(User, { foreignKey: 'created_by', as: 'creator', onDelete: 'CASCADE' });
  Farm.hasMany(MarketContract, { foreignKey: 'farm_id', as: 'marketContracts' });
  Supplier.hasMany(MarketContract, { foreignKey: 'supplier_id', as: 'supplierContracts' });
  User.hasMany(MarketContract, { foreignKey: 'created_by', as: 'createdContracts', onDelete: 'CASCADE' });

  // PriceComparison associations
  PriceComparison.belongsTo(Farm, { foreignKey: 'farm_id', as: 'priceComparisonFarm' });
  PriceComparison.belongsTo(Product, { foreignKey: 'product_id', as: 'priceComparisonProduct' });
  PriceComparison.belongsTo(Supplier, { foreignKey: 'best_price_supplier_id', as: 'bestPriceSupplier' });
  Farm.hasMany(PriceComparison, { foreignKey: 'farm_id', as: 'priceComparisons' });
  Product.hasMany(PriceComparison, { foreignKey: 'product_id', as: 'priceComparisons' });
  Supplier.hasMany(PriceComparison, { foreignKey: 'best_price_supplier_id', as: 'bestPriceComparisons' });

  // MarketTrend associations
  MarketTrend.belongsTo(Farm, { foreignKey: 'farm_id', as: 'marketTrendFarm' });
  MarketTrend.belongsTo(Product, { foreignKey: 'product_id', as: 'marketTrendProduct' });
  Farm.hasMany(MarketTrend, { foreignKey: 'farm_id', as: 'marketTrends' });
  Product.hasMany(MarketTrend, { foreignKey: 'product_id', as: 'marketTrends' });

  // MarketplaceListing associations
  MarketplaceListing.belongsTo(Farm, { foreignKey: 'farm_id', as: 'marketplaceListingFarm' });
  MarketplaceListing.belongsTo(User, { foreignKey: 'created_by', as: 'creator', onDelete: 'CASCADE' });
  MarketplaceListing.belongsTo(Product, { foreignKey: 'product_id', as: 'marketplaceListingProduct' });
  Farm.hasMany(MarketplaceListing, { foreignKey: 'farm_id', as: 'marketplaceListings' });
  User.hasMany(MarketplaceListing, { foreignKey: 'created_by', as: 'createdListings', onDelete: 'CASCADE' });
  Product.hasMany(MarketplaceListing, { foreignKey: 'product_id', as: 'marketplaceListings' });

  // Market Price associations
  MarketPrice.belongsTo(Farm, { foreignKey: 'farm_id', as: 'marketPriceFarm' });
  Farm.hasMany(MarketPrice, { foreignKey: 'farm_id', as: 'marketPrices' });

  // Future Price associations
  FuturePrice.belongsTo(Farm, { foreignKey: 'farm_id', as: 'futurePriceFarm' });
  Farm.hasMany(FuturePrice, { foreignKey: 'farm_id', as: 'futurePrices' });

  // Historical Price associations
  HistoricalPrice.belongsTo(Farm, { foreignKey: 'farm_id', as: 'historicalPriceFarm' });
  Farm.hasMany(HistoricalPrice, { foreignKey: 'farm_id', as: 'historicalPrices' });

  // Tax System associations

  // TaxCategory associations
  TaxCategory.belongsTo(Farm, { foreignKey: 'farm_id', as: 'taxCategoryFarm' });
  Farm.hasMany(TaxCategory, { foreignKey: 'farm_id', as: 'taxCategories' });

  // TaxDeduction associations
  TaxDeduction.belongsTo(Farm, { foreignKey: 'farm_id', as: 'taxDeductionFarm' });
  Farm.hasMany(TaxDeduction, { foreignKey: 'farm_id', as: 'taxDeductions' });

  // TaxDocument associations
  TaxDocument.belongsTo(Farm, { foreignKey: 'farm_id', as: 'taxDocumentFarm' });
  TaxDocument.belongsTo(User, { foreignKey: 'created_by', as: 'creator', onDelete: 'CASCADE' });
  Farm.hasMany(TaxDocument, { foreignKey: 'farm_id', as: 'taxDocuments' });
  User.hasMany(TaxDocument, { foreignKey: 'created_by', as: 'createdTaxDocuments', onDelete: 'CASCADE' });

  // EmployeeTaxInfo associations
  EmployeeTaxInfo.belongsTo(Employee, { foreignKey: 'employee_id', as: 'employee' });
  EmployeeTaxInfo.belongsTo(Farm, { foreignKey: 'farm_id', as: 'employeeTaxInfoFarm' });
  EmployeeTaxInfo.belongsTo(TaxDocument, { foreignKey: 'w2_document_id', as: 'w2Document' });
  Employee.hasMany(EmployeeTaxInfo, { foreignKey: 'employee_id', as: 'taxInfo' });
  Farm.hasMany(EmployeeTaxInfo, { foreignKey: 'farm_id', as: 'employeeTaxInfo' });
  TaxDocument.hasOne(EmployeeTaxInfo, { foreignKey: 'w2_document_id', as: 'employeeTaxInfo' });

  // ContractorTaxInfo associations
  ContractorTaxInfo.belongsTo(Farm, { foreignKey: 'farm_id', as: 'contractorTaxInfoFarm' });
  ContractorTaxInfo.belongsTo(TaxDocument, { foreignKey: 'form_1099_document_id', as: 'form1099Document' });
  Farm.hasMany(ContractorTaxInfo, { foreignKey: 'farm_id', as: 'contractorTaxInfo' });
  TaxDocument.hasOne(ContractorTaxInfo, { foreignKey: 'form_1099_document_id', as: 'contractorTaxInfo' });

  // TaxPayment associations
  TaxPayment.belongsTo(Farm, { foreignKey: 'farm_id', as: 'taxPaymentFarm' });
  TaxPayment.belongsTo(User, { foreignKey: 'created_by', as: 'creator', onDelete: 'CASCADE' });
  TaxPayment.belongsTo(TaxDocument, { foreignKey: 'receipt_document_id', as: 'receiptDocument' });
  Farm.hasMany(TaxPayment, { foreignKey: 'farm_id', as: 'taxPayments' });
  User.hasMany(TaxPayment, { foreignKey: 'created_by', as: 'createdTaxPayments', onDelete: 'CASCADE' });
  TaxDocument.hasOne(TaxPayment, { foreignKey: 'receipt_document_id', as: 'taxPayment' });

  // TaxFiling associations
  TaxFiling.belongsTo(Farm, { foreignKey: 'farm_id', as: 'taxFilingFarm' });
  TaxFiling.belongsTo(User, { foreignKey: 'created_by', as: 'creator', onDelete: 'CASCADE' });
  TaxFiling.belongsTo(TaxDocument, { foreignKey: 'document_id', as: 'filingDocument' });
  Farm.hasMany(TaxFiling, { foreignKey: 'farm_id', as: 'taxFilings' });
  User.hasMany(TaxFiling, { foreignKey: 'created_by', as: 'createdTaxFilings', onDelete: 'CASCADE' });
  TaxDocument.hasOne(TaxFiling, { foreignKey: 'document_id', as: 'taxFiling' });

  // Document Signing System associations

  // SignableDocument associations
  SignableDocument.belongsTo(Farm, { foreignKey: 'farm_id', as: 'signableDocumentFarm' });
  SignableDocument.belongsTo(User, { foreignKey: 'created_by', as: 'creator' });
  Farm.hasMany(SignableDocument, { foreignKey: 'farm_id', as: 'signableDocuments' });
  User.hasMany(SignableDocument, { foreignKey: 'created_by', as: 'createdSignableDocuments' });

  // DocumentSigner associations
  // DocumentSigner.belongsTo(SignableDocument, { foreignKey: 'document_id', as: 'signerDocument' }); // Removed to avoid duplicate association
  SignableDocument.hasMany(DocumentSigner, { foreignKey: 'document_id', as: 'signers' });

  // DocumentSignature associations
  // DocumentSignature.belongsTo(SignableDocument, { foreignKey: 'document_id', as: 'signatureDocument' }); // Removed to avoid duplicate association with the one in DocumentSignature.js
  // DocumentSignature.belongsTo(DocumentSigner, { foreignKey: 'signer_id', as: 'signer' }); // Removed to avoid duplicate association with the one in DocumentSignature.js
  SignableDocument.hasMany(DocumentSignature, { foreignKey: 'document_id', as: 'signatures' });
  DocumentSigner.hasMany(DocumentSignature, { foreignKey: 'signer_id', as: 'signatures' });

  // DocumentField associations
  // DocumentField.belongsTo(SignableDocument, { foreignKey: 'document_id', as: 'fieldDocument' }); // Removed to avoid duplicate association with the one in DocumentField.js
  // DocumentField.belongsTo(DocumentSigner, { foreignKey: 'signer_id', as: 'signer' }); // Removed to avoid duplicate association with the one in DocumentField.js
  SignableDocument.hasMany(DocumentField, { foreignKey: 'document_id', as: 'fields' });
  DocumentSigner.hasMany(DocumentField, { foreignKey: 'signer_id', as: 'fields' });

  // DocumentAuditLog associations
  // DocumentAuditLog.belongsTo(SignableDocument, { foreignKey: 'document_id', as: 'auditLogDocument' }); // Removed to avoid duplicate association with the one in DocumentAuditLog.js
  // DocumentAuditLog.belongsTo(DocumentSigner, { foreignKey: 'signer_id', as: 'signer' }); // Removed to avoid duplicate association with the one in DocumentAuditLog.js
  // DocumentAuditLog.belongsTo(User, { foreignKey: 'user_id', as: 'user' }); // Removed to avoid duplicate association with the one in DocumentAuditLog.js
  SignableDocument.hasMany(DocumentAuditLog, { foreignKey: 'document_id', as: 'auditLogs' });
  DocumentSigner.hasMany(DocumentAuditLog, { foreignKey: 'signer_id', as: 'auditLogs' });
  User.hasMany(DocumentAuditLog, { foreignKey: 'user_id', as: 'documentAuditLogs' });

  // DocumentBlockchainVerification associations
  DocumentBlockchainVerification.belongsTo(SignableDocument, { foreignKey: 'document_id', as: 'blockchainVerificationDocument' });
  SignableDocument.hasMany(DocumentBlockchainVerification, { foreignKey: 'document_id', as: 'blockchainVerifications' });

  // Invoice associations
  Invoice.belongsTo(Customer, { foreignKey: 'customer_id' });
  Customer.hasMany(Invoice, { foreignKey: 'customer_id' });

  // InvoiceItem associations
  InvoiceItem.belongsTo(Invoice, { foreignKey: 'invoice_id', onDelete: 'CASCADE' });
  Invoice.hasMany(InvoiceItem, { foreignKey: 'invoice_id', onDelete: 'CASCADE' });

  // InvoiceItem-Product associations
  InvoiceItem.belongsTo(Product, { foreignKey: 'product_id' });
  Product.hasMany(InvoiceItem, { foreignKey: 'product_id' });
};
